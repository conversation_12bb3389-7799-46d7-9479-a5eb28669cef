#!/usr/bin/env python3
"""
Test script to verify that message duplication issue is fixed.
"""

import asyncio
import json
import uuid
import aiohttp
from typing import Dict, Any

BASE_URL = "http://localhost:7861"


async def create_conversation() -> str:
    """Create a new conversation and return its ID"""
    payload = {"title": "Test Conversation"}
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{BASE_URL}/api/conversations", json=payload
        ) as response:
            if response.status == 201:
                data = await response.json()
                return data["conversation_id"]
            else:
                text = await response.text()
                raise Exception(
                    f"Failed to create conversation: {response.status} - {text}"
                )


async def send_message(conversation_id: str, message: str) -> Dict[str, Any]:
    """Send a message to the bot and return the response"""
    payload = {
        "conversation_id": conversation_id,
        "actions": [
            {
                "label": "rtvi-ai",
                "type": "action",
                "id": str(uuid.uuid4())[:8],
                "data": {
                    "service": "llm",
                    "action": "append_to_messages",
                    "arguments": [
                        {
                            "name": "messages",
                            "value": [{"role": "user", "content": [{"text": message}]}],
                        }
                    ],
                },
            }
        ],
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(f"{BASE_URL}/api/bot/action", json=payload) as response:
            if response.status == 200:
                # Read the streaming response
                content = ""
                async for line in response.content:
                    line_str = line.decode("utf-8").strip()
                    if line_str.startswith("data: "):
                        content += line_str[6:] + "\n"
                return {"status": "success", "content": content}
            else:
                text = await response.text()
                return {"status": "error", "content": text}


async def get_conversation_messages(conversation_id: str) -> Dict[str, Any]:
    """Get all messages for a conversation"""
    async with aiohttp.ClientSession() as session:
        async with session.get(
            f"{BASE_URL}/api/conversations/{conversation_id}/messages"
        ) as response:
            if response.status == 200:
                return await response.json()
            else:
                raise Exception(f"Failed to get messages: {response.status}")


async def test_message_duplication():
    """Test that messages are not duplicated when continuing a conversation"""
    print("🧪 Testing message duplication fix...")

    # Create a new conversation
    print("1. Creating new conversation...")
    conversation_id = await create_conversation()
    print(f"   Created conversation: {conversation_id}")

    # Send first message
    print("2. Sending first message: 'Hello, what can you do?'")
    response1 = await send_message(conversation_id, "Hello, what can you do?")
    print(f"   Response status: {response1['status']}")

    # Wait a bit for processing
    await asyncio.sleep(2)

    # Get messages after first interaction
    messages1 = await get_conversation_messages(conversation_id)
    print(f"   Messages after first interaction: {len(messages1['messages'])}")
    for i, msg in enumerate(messages1["messages"], 1):
        role = msg["content"].get("role", "unknown")
        content_text = ""
        if isinstance(msg["content"].get("content"), list):
            for item in msg["content"]["content"]:
                if item.get("type") == "text":
                    content_text = item.get("text", "")[:50] + "..."
                    break
        elif isinstance(msg["content"].get("content"), str):
            content_text = msg["content"]["content"][:50] + "..."
        print(f"     Message {i} ({role}): {content_text}")

    # Send second message
    print("3. Sending second message: 'Tell me a joke'")
    response2 = await send_message(conversation_id, "Tell me a joke")
    print(f"   Response status: {response2['status']}")

    # Wait a bit for processing
    await asyncio.sleep(2)

    # Get messages after second interaction
    messages2 = await get_conversation_messages(conversation_id)
    print(f"   Messages after second interaction: {len(messages2['messages'])}")
    for i, msg in enumerate(messages2["messages"], 1):
        role = msg["content"].get("role", "unknown")
        content_text = ""
        if isinstance(msg["content"].get("content"), list):
            for item in msg["content"]["content"]:
                if item.get("type") == "text":
                    content_text = item.get("text", "")[:50] + "..."
                    break
        elif isinstance(msg["content"].get("content"), str):
            content_text = msg["content"]["content"][:50] + "..."
        print(f"     Message {i} ({role}): {content_text}")

    # Check for duplicates
    print("4. Checking for message duplication...")
    expected_messages = (
        4  # user1 + assistant1 + user2 + assistant2 (system message not stored)
    )
    actual_messages = len(messages2["messages"])

    if actual_messages == expected_messages:
        print("   ✅ SUCCESS: No message duplication detected!")
        print(f"   Expected {expected_messages} messages, got {actual_messages}")
    else:
        print("   ❌ FAILURE: Message duplication detected!")
        print(f"   Expected {expected_messages} messages, got {actual_messages}")

        # Check for specific duplicates
        message_contents = []
        for msg in messages2["messages"]:
            content = msg["content"]
            if isinstance(content.get("content"), list):
                for item in content["content"]:
                    if item.get("type") == "text":
                        message_contents.append((content.get("role"), item.get("text")))
            elif isinstance(content.get("content"), str):
                message_contents.append((content.get("role"), content["content"]))

        # Find duplicates
        seen = set()
        duplicates = set()
        for role, text in message_contents:
            key = (role, text)
            if key in seen:
                duplicates.add(key)
            seen.add(key)

        if duplicates:
            print("   Duplicate messages found:")
            for role, text in duplicates:
                print(f"     - {role}: {text[:100]}...")

    return actual_messages == expected_messages


if __name__ == "__main__":
    asyncio.run(test_message_duplication())
