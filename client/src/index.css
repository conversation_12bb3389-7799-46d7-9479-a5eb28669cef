@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --sidebar-width: 320px;

  --primary: 156 72% 60%;
  --primary-foreground: 0 0% 10%;
  --accent: var(--primary);
  --accent-foreground: var(--primary-foreground);
  --ring: var(--primary);
}

body {
  color: hsl(var(--foreground));
  background: hsl(var(--background));
  font-family: "Inter Variable", sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .borderClip {
    background-clip: padding-box, border-box;
  }
}

@layer base {
  .light,
  :root:not(.dark) {
    --background: 0 0% 100%;
    --foreground: 240 6% 10%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 13%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 13%;
    --secondary: 240 5% 96%;
    --secondary-foreground: 240 6% 10%;
    --muted: 240 5% 65%;
    --muted-foreground: 0 0% 46%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 240 6% 90%;
    --input: 0 0% 89.8%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 10%;
    --foreground: 0 0% 90%;
    --card: 0 0% 15%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 15%;
    --popover-foreground: 0 0% 98%;
    --secondary: 0 0% 18%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 18%;
    --muted-foreground: 0 0% 63.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 18%;
    --input: 0 0% 15%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
  @media (prefers-color-scheme: dark) {
    :root:not(.light) {
      --background: 0 0% 10%;
      --foreground: 0 0% 90%;
      --card: 0 0% 15%;
      --card-foreground: 0 0% 98%;
      --popover: 0 0% 15%;
      --popover-foreground: 0 0% 98%;
      --secondary: 0 0% 18%;
      --secondary-foreground: 0 0% 98%;
      --muted: 0 0% 18%;
      --muted-foreground: 0 0% 63.9%;
      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 0 0% 98%;
      --border: 0 0% 18%;
      --input: 0 0% 15%;
      --chart-1: 220 70% 50%;
      --chart-2: 160 60% 45%;
      --chart-3: 30 80% 55%;
      --chart-4: 280 65% 60%;
      --chart-5: 340 75% 55%;
    }
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
