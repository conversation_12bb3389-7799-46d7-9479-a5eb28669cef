import { ClientPage } from "@/components/ClientPage";
import ErrorPage from "@/components/ErrorPage";
import { Layout } from "@/components/Layout";
import QueryClientProvider from "@/components/QueryClientProvider";
import { Toaster } from "@/components/ui/toaster";
import { AppStateProvider } from "@/contexts/AppStateProvider";
import { LoaderCircleIcon } from "lucide-react";
import { useEffect, useState } from "react";

function App() {
  const [websocketEnabled, setWebsocketEnabled] = useState<boolean>();
  const [webrtcEnabled, setWebrtcEnabled] = useState<boolean>();
  const [dailyEnabled, setDailyEnabled] = useState<boolean>();

  useEffect(() => {
    const abort = new AbortController();
    fetch(`${import.meta.env.VITE_SERVER_URL}/`, {
      signal: abort.signal,
    })
      .then((response) => response.json())
      .then((json) => {
        setWebsocketEnabled(json?.["websocket-enabled"] ?? false);
        setWebrtcEnabled(json?.["webrtc-enabled"] ?? false);
        setDailyEnabled(json?.["daily-enabled"] ?? false);
      })
      .catch(() => {
        setWebsocketEnabled(false);
        setWebrtcEnabled(false);
        setDailyEnabled(false);
      });
  }, []);

  if (websocketEnabled === undefined && webrtcEnabled === undefined) {
    return (
      <Layout>
        <div className="h-full flex items-center justify-center">
          <LoaderCircleIcon className="animate-spin" />
        </div>
      </Layout>
    );
  }

  if (!websocketEnabled && !webrtcEnabled) {
    return (
      <ErrorPage title="Missing configuration">
        <code>WebRTC</code> and{" "} <code>WebSocket</code> disabled.
      </ErrorPage>
    );
  }

  return (
    <QueryClientProvider>
      <AppStateProvider
        dailyEnabled={dailyEnabled ?? false}
        webrtcEnabled={webrtcEnabled ?? false}
        websocketEnabled={websocketEnabled ?? false}
      >
        <Layout>
          <ClientPage />
          <Toaster />
        </Layout>
      </AppStateProvider>
    </QueryClientProvider>
  );
}

export default App;
