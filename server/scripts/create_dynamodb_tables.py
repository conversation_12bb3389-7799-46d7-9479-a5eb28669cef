#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create DynamoDB tables for the Voice Agent application.

This script must be run before starting the server for the first time.
It creates the necessary DynamoDB tables:
- Conversations
- Messages
- Attachments

Usage:
    python scripts/create_dynamodb_tables.py
"""

import asyncio
import os
import sys

# Add the parent directory to the path so we can import from server
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.dynamodb import default_dynamodb_factory


async def main():
    """Create DynamoDB tables if they don't exist"""
    print("🚀 Voice Agent - DynamoDB Table Initialization")
    print("=" * 50)
    print("Checking AWS credentials and initializing DynamoDB tables...")

    try:
        await default_dynamodb_factory.initialize_tables()
        print("\n✅ DynamoDB tables initialized successfully!")
        print("\n📋 Tables created/verified:")
        print("   • Conversations")
        print("   • Messages")
        print("   • Attachments")
        print("\n🎉 You can now start the server with: python server.py")
    except Exception as e:
        print(f"\n❌ Error initializing DynamoDB tables: {str(e)}")
        print("\n💡 Please check:")
        print("   • AWS credentials are properly configured in .env file")
        print("   • AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are set")
        print("   • AWS_REGION is set (default: us-east-1)")
        print("   • Your AWS account has DynamoDB permissions")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
