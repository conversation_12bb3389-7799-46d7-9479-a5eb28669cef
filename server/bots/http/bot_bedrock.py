import asyncio
from typing import Any, Async<PERSON>enerator, List, Tuple

from bots.http.frame_serializer import BotFrameSerializer
from bots.http.persistent_context import PersistentContext
from bots.http.rtvi import create_rtvi_processor
from bots.types import BotConfig, BotParams
from common.config import SERVICE_API_KEYS, AWS_CONFIG, get_language_code
from common.dynamodb import (
    default_dynamodb_factory,
    get_max_message_number,
    MESSAGES_TABLE,
    ATTACHMENTS_TABLE,
)
from common.models import AttachmentModel, Message, MessageCreateModel
from fastapi import HTTPException, status
from loguru import logger
from openai._types import NOT_GIVEN

from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask, PipelineParams
from pipecat.processors.async_generator import AsyncGeneratorProcessor
from pipecat.processors.frameworks.rtvi import (
    RTVIConfig,
    RTVIObserver,
    R<PERSON><PERSON>ctionRun,
    R<PERSON><PERSON>essage,
    RTVIProcessor,
)

from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext
from pipecat.services.aws.llm import AWSBedrockLLMService

SYSTEM_INSTRUCTION = """You are Nova, a friendly, helpful embodied robot.
Your goal is to demonstrate your capabilities in a succinct way.
Your output will be converted to audio so don't include special characters in your answers.
Respond to what the user said in a creative and helpful way. Keep your responses brief. One or two sentences at most.
"""


async def http_bedrock_bot_pipeline(
    params: BotParams,
    config: BotConfig,
    messages,
    attachments: List[AttachmentModel],
    language_code: str = "english",
) -> Tuple[AsyncGenerator[Any, None], Any]:
    """HTTP pipeline using AWS Bedrock for LLM, Transcribe for STT, and Polly for TTS"""

    # Check for AWS credentials
    aws_access_key = SERVICE_API_KEYS.get("aws")
    if aws_access_key is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Service `aws` not available in SERVICE_API_KEYS. Please check your environment variables.",
        )

    llm = AWSBedrockLLMService(
        aws_region=AWS_CONFIG["region"],
        model="apac.amazon.nova-lite-v1:0",
        params=AWSBedrockLLMService.InputParams(temperature=0.8, latency="standard"),
    )

    system_message = [
        {
            "role": "system",
            "content": SYSTEM_INSTRUCTION,
        },
    ]

    tools = NOT_GIVEN
    logger.debug(f"Messages: {messages}")

    # filter out system messages
    messages = [msg for msg in messages if msg.get("role") != "system"]

    # combine system message and user messages
    context = OpenAILLMContext(system_message + messages, tools)
    context_aggregator = llm.create_context_aggregator(context)

    # Calculate the number of existing messages to avoid re-storing them
    # This includes the system message plus any loaded messages from DynamoDB
    existing_messages_count = len(system_message) + len(messages)
    storage = PersistentContext(
        context=context, existing_messages_count=existing_messages_count
    )

    async_generator = AsyncGeneratorProcessor(serializer=BotFrameSerializer())

    #
    # RTVI events for Pipecat client UI
    #

    rtvi = await create_rtvi_processor(config, context_aggregator.user())

    #
    # RTVI events for Pipecat client UI
    #
    # rtvi = RTVIProcessor(config=RTVIConfig(config=[]))

    processors = [
        rtvi,
        context_aggregator.user(),
        storage.create_processor(),
        llm,
        async_generator,
        context_aggregator.assistant(),
        storage.create_processor(exit_on_endframe=True),
    ]

    pipeline = Pipeline(processors)

    runner = PipelineRunner(handle_sigint=False)

    task = PipelineTask(
        pipeline,
        params=PipelineParams(
            allow_interruptions=True,
            enable_metrics=True,
            enable_usage_metrics=True,
        ),
        observers=[RTVIObserver(rtvi)],
    )

    runner_task = asyncio.create_task(runner.run(task))

    @storage.on_context_message
    async def on_context_message(messages: list[Any]):
        logger.debug(
            f"{len(messages)} message(s) received for storage: {str(messages)}"
        )
        try:
            await store_messages_to_dynamodb(
                conversation_id=params.conversation_id,
                messages=messages,
                language_code=language_code,
            )
        except Exception as e:
            logger.error(f"Error storing messages: {e}")
            raise e

    @rtvi.event_handler("on_bot_started")
    async def on_bot_started(rtvi: RTVIProcessor):
        for action in params.actions:
            logger.debug(f"Processing action: {action}")

            # If this is an append_to_messages action, we need to append any
            # attachments. The rule we'll follow is that we should append
            # attachments to the first "user" message in the actions list.
            if action.data.get("action") == "append_to_messages" and attachments:
                for msg in action.data["arguments"][0]["value"]:
                    if msg.get("role") == "user":
                        # Append attachments to this message
                        logger.debug(
                            f"Appending {len(attachments)} attachment(s) to 'user' message"
                        )
                        content = msg.get("content", "")
                        if isinstance(content, str):
                            content = [{"type": "text", "text": content}]
                        for attachment in attachments:
                            # Assume for the moment that all attachments are images
                            content.append(
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:{attachment.file_type};base64,{attachment.file_data}"
                                    },
                                }
                            )
                            # Delete attachment from DynamoDB
                            await delete_attachment_from_dynamodb(
                                attachment.attachment_id
                            )
                        break

            await rtvi.handle_message(action)

        # This is a single turn, so we just push an action to stop the running
        # pipeline task.
        action = RTVIActionRun(service="system", action="end")
        message = RTVIMessage(type="action", id="END", data=action.model_dump())
        await rtvi.handle_message(message)

    return (async_generator.generator(), runner_task)


async def store_messages_to_dynamodb(
    conversation_id: str,
    messages: List[Any],
    language_code: str = "english",
):
    """Store messages to DynamoDB"""
    try:
        table = default_dynamodb_factory.get_table(MESSAGES_TABLE)

        # Get the current max message number for this conversation
        max_message_number = await get_max_message_number(conversation_id)

        for i, message in enumerate(messages):
            message_number = max_message_number + i + 1
            # Create a MessageCreateModel from the message content
            message_create = MessageCreateModel(content=message)
            message_item = Message.to_dynamodb(
                message=message_create,
                conversation_id=conversation_id,
                message_number=message_number,
            )
            message_item["language_code"] = language_code

            table.put_item(Item=message_item)
            logger.debug(
                f"Stored message {message_number} for conversation {conversation_id}"
            )

    except Exception as e:
        logger.error(f"Error storing messages to DynamoDB: {str(e)}")
        raise e


async def delete_attachment_from_dynamodb(attachment_id: str):
    """Delete attachment from DynamoDB"""
    try:
        table = default_dynamodb_factory.get_table(ATTACHMENTS_TABLE)
        table.delete_item(Key={"attachment_id": attachment_id})
        logger.debug(f"Deleted attachment {attachment_id}")
    except Exception as e:
        logger.error(f"Error deleting attachment {attachment_id}: {str(e)}")
        # Don't raise the error as this is not critical
