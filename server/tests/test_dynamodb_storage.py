#!/usr/bin/env python3
"""
Test script for the DynamoDB Conversation Storage.

This script tests the functionality of the DynamoDB Conversation Storage
by simulating a conversation and verifying that the data is stored correctly.
"""

import asyncio
import os
import sys
import uuid
from datetime import datetime

# Add the parent directory to the path so we can import from server
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.dynamodb import (
    CONVERSATIONS_TABLE,
    MESSAGES_TABLE,
    default_dynamodb_factory,
    get_conversation_by_id,
    get_messages_by_conversation_id,
)
from processors.dynamodb_conversation_storage import (
    DynamoDBConversationStorage,
    get_storage,
)


async def main():
    """Test the DynamoDB Conversation Storage."""
    print("Initializing DynamoDB tables...")
    await default_dynamodb_factory.initialize_tables()
    print("DynamoDB tables initialized")

    # Generate a unique conversation ID for testing
    conversation_id = str(uuid.uuid4())
    print(f"Testing with conversation ID: {conversation_id}")

    # Create the storage
    storage = get_storage(
        conversation_id=conversation_id,
        conversation_title="Test Conversation",
        store_system_messages=True,
    )

    # Test system message
    await storage.store_message("system", "You are a helpful assistant.")
    print("Stored system message")

    # Test user message
    await storage.store_message("user", "Hello, how are you?")
    print("Stored user message")

    # Test assistant message
    await storage.store_message("assistant", "I'm doing well, thank you for asking!")
    print("Stored assistant message")

    # Verify the conversation was created
    conversation = await get_conversation_by_id(conversation_id)
    if not conversation:
        print("ERROR: Conversation not found in DynamoDB")
        return

    print("\nConversation details:")
    print(f"  ID: {conversation['conversation_id']}")
    print(f"  Title: {conversation.get('title', 'No title')}")
    print(f"  Created: {conversation.get('created_at', 'Unknown')}")
    print(f"  Updated: {conversation.get('updated_at', 'Unknown')}")

    # Verify the messages were created
    messages = await get_messages_by_conversation_id(conversation_id)
    print(f"\nFound {len(messages)} messages:")

    for i, message in enumerate(messages, 1):
        content = message.get("content", {})
        role = content.get("role", "unknown")
        text = content.get("content", "No content")
        print(f"  Message {i} ({role}): {text}")

    # Clean up (optional - comment out to keep the test data)
    print("\nCleaning up test data...")
    table = default_dynamodb_factory.get_table(CONVERSATIONS_TABLE)
    table.delete_item(Key={"conversation_id": conversation_id})

    messages_table = default_dynamodb_factory.get_table(MESSAGES_TABLE)
    for message in messages:
        messages_table.delete_item(Key={"message_id": message["message_id"]})

    print("Test completed successfully!")


if __name__ == "__main__":
    asyncio.run(main())
