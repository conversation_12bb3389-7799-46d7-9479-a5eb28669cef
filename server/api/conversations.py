import base64
import mimetypes
from datetime import datetime
from typing import Dict, List, Optional

import boto3
from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError
from fastapi import APIRouter, BackgroundTasks, Depends, File, HTTPException, UploadFile, status
from loguru import logger

from common.dynamodb import (
    ATTACHMENTS_TABLE,
    CONVERSATIONS_TABLE,
    MESSAGES_TABLE,
    default_dynamodb_factory,
    get_conversation_by_id,
    get_max_message_number,
    get_messages_by_conversation_id,
)
from common.models import (
    Attachment,
    AttachmentModel,
    AttachmentUploadResponse,
    Conversation,
    ConversationCreateModel,
    ConversationModel,
    ConversationUpdateModel,
    Message,
    MessageCreateModel,
    MessageModel,
)

router = APIRouter(prefix="/conversations")


@router.get("", response_model=List[ConversationModel], name="Get Conversations")
async def get_conversations(
    page: int = 1,
    per_page: int = 10,
    archived: bool = False,
    q: str | None = None,
):
    """
    Retrieve a list of conversations with optional pagination and filtering.

    Args:
        page (int): The page number for pagination. Defaults to 1.
        per_page (int): The number of items per page for pagination. Defaults to 10.
        archived (bool): Filter conversations by archived status. Defaults to False.
        q (str | None): Optional query parameter to search for a conversation by ID.

    Returns:
        list[ConversationModel]: A list of conversation models.

    Raises:
        HTTPException: If the page or per_page is less than 1.
        HTTPException: If a conversation with the specified ID is not found.
    """
    if page < 1:
        raise HTTPException(status_code=400, detail="Page must be greater than 0")
    if per_page < 1:
        raise HTTPException(status_code=400, detail="Per page must be greater than 0")

    # If a query is provided, search by conversation ID
    if q:
        conversation = await get_conversation_by_id(q)
        if conversation:
            return [Conversation.from_dynamodb(conversation)]
        else:
            raise HTTPException(status_code=404, detail="Conversation not found")

    try:
        # Calculate offset
        offset = (page - 1) * per_page

        # Get all conversations from DynamoDB
        table = default_dynamodb_factory.get_table(CONVERSATIONS_TABLE)
        response = table.scan()
        items = response.get("Items", [])

        # Filter by archived status
        filtered_items = [item for item in items if item.get("archived", False) == archived]

        # Sort by updated_at in descending order
        sorted_items = sorted(
            filtered_items,
            key=lambda x: x.get("updated_at", ""),
            reverse=True,
        )

        # Apply pagination
        paginated_items = sorted_items[offset : offset + per_page]

        # Convert to Pydantic models
        return [Conversation.from_dynamodb(item) for item in paginated_items]
    except Exception as e:
        logger.error(f"Error getting conversations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting conversations: {str(e)}")


@router.post("", response_model=ConversationModel, status_code=status.HTTP_201_CREATED)
async def create_conversation(conversation: ConversationCreateModel):
    """
    Create a new conversation.

    Args:
        conversation (ConversationCreateModel): The conversation data.

    Returns:
        ConversationModel: The newly created conversation.
    """
    try:
        # Convert to DynamoDB item
        item = Conversation.to_dynamodb(conversation)
        
        # Save to DynamoDB
        table = default_dynamodb_factory.get_table(CONVERSATIONS_TABLE)
        table.put_item(Item=item)
        
        # Return the created conversation
        return Conversation.from_dynamodb(item)
    except Exception as e:
        logger.error(f"Error creating conversation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create conversation: {str(e)}",
        )


@router.delete("/{conversation_id}", name="Delete Conversation")
async def delete_conversation(conversation_id: str):
    """
    Delete a conversation by its ID.

    Args:
        conversation_id (str): The unique identifier of the conversation to delete.

    Returns:
        dict: A message confirming successful deletion.

    Raises:
        HTTPException: If the conversation with the specified ID is not found (404).
    """
    # Check if conversation exists
    conversation = await get_conversation_by_id(conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    try:
        # Delete the conversation
        table = default_dynamodb_factory.get_table(CONVERSATIONS_TABLE)
        table.delete_item(Key={"conversation_id": conversation_id})
        
        # Delete all messages for this conversation
        messages = await get_messages_by_conversation_id(conversation_id)
        messages_table = default_dynamodb_factory.get_table(MESSAGES_TABLE)
        
        for message in messages:
            messages_table.delete_item(Key={"message_id": message["message_id"]})
        
        return {"message": "Conversation deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting conversation {conversation_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete conversation: {str(e)}",
        )


@router.put("/{conversation_id}", response_model=ConversationModel, name="Update Conversation")
async def update_conversation(
    conversation_id: str,
    conversation_update: ConversationUpdateModel,
):
    """
    Update a conversation by its ID.

    Args:
        conversation_id (str): The unique identifier of the conversation to update.
        conversation_update (ConversationUpdateModel): The updated conversation data.

    Returns:
        ConversationModel: The updated conversation.

    Raises:
        HTTPException: If the conversation with the specified ID is not found (404).
    """
    # Check if conversation exists
    conversation = await get_conversation_by_id(conversation_id)
    if not conversation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")

    try:
        # Update only the provided fields
        update_data = {k: v for k, v in conversation_update.model_dump(exclude_unset=True).items()}
        
        if not update_data:
            # No changes to make
            return Conversation.from_dynamodb(conversation)
        
        # Update the conversation
        updated_item = Conversation.update_dynamodb(conversation, update_data)
        
        # Save to DynamoDB
        table = default_dynamodb_factory.get_table(CONVERSATIONS_TABLE)
        table.put_item(Item=updated_item)
        
        # Return the updated conversation
        return Conversation.from_dynamodb(updated_item)
    except Exception as e:
        logger.error(f"Error updating conversation {conversation_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update conversation: {str(e)}",
        )


@router.get(
    "/{conversation_id}/messages", response_model=Dict, name="Get Conversation and Messages"
)
async def get_conversation_messages(
    conversation_id: str,
    background_tasks: BackgroundTasks,
):
    """
    Retrieve a conversation and its associated messages by conversation ID.

    Args:
        conversation_id (str): The unique identifier of the conversation to retrieve.
        background_tasks (BackgroundTasks): FastAPI background tasks.

    Returns:
        dict: A dictionary containing the conversation and a list of its messages.

    Raises:
        HTTPException: If the conversation with the specified ID is not found (404).
    """
    # Check if conversation exists
    conversation = await get_conversation_by_id(conversation_id)
    if not conversation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")

    try:
        # Get all messages for this conversation
        messages = await get_messages_by_conversation_id(conversation_id)
        
        # Convert to Pydantic models
        conversation_model = Conversation.from_dynamodb(conversation)
        message_models = [Message.from_dynamodb(message) for message in messages]
        
        # Sort messages by message_number
        message_models.sort(key=lambda x: x.message_number)
        
        return {
            "conversation": conversation_model,
            "messages": message_models,
        }
    except Exception as e:
        logger.error(f"Error getting conversation messages {conversation_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get conversation messages: {str(e)}",
        )


@router.post(
    "/{conversation_id}/messages", response_model=MessageModel, status_code=status.HTTP_201_CREATED
)
async def create_message(
    conversation_id: str,
    message: MessageCreateModel,
):
    """
    Create a new message in a specified conversation.

    Args:
        conversation_id (str): The unique identifier of the conversation to add the message to.
        message (MessageCreateModel): The message data containing content and optional metadata.

    Returns:
        MessageModel: The newly created message with all its fields populated.

    Raises:
        HTTPException: If the conversation with the specified ID is not found (404).
    """
    # Check if conversation exists
    conversation = await get_conversation_by_id(conversation_id)
    if not conversation:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Conversation not found")

    try:
        # Get the next message number
        message_number = await get_max_message_number(conversation_id) + 1
        
        # Convert to DynamoDB item
        item = Message.to_dynamodb(message, conversation_id, message_number)
        
        # Save to DynamoDB
        messages_table = default_dynamodb_factory.get_table(MESSAGES_TABLE)
        messages_table.put_item(Item=item)
        
        # Update the conversation's updated_at timestamp
        conversation["updated_at"] = datetime.utcnow().isoformat()
        conversations_table = default_dynamodb_factory.get_table(CONVERSATIONS_TABLE)
        conversations_table.put_item(Item=conversation)
        
        # Return the created message
        return Message.from_dynamodb(item)
    except Exception as e:
        logger.error(f"Error creating message in conversation {conversation_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create message: {str(e)}",
        )


@router.post("/upload", response_model=AttachmentUploadResponse)
async def upload_file(file: UploadFile = File(...)):
    """
    Upload a file as an attachment.

    Args:
        file (UploadFile): The file to upload.

    Returns:
        AttachmentUploadResponse: The attachment ID and file type.

    Raises:
        HTTPException: If the file is too large or there's an error uploading.
    """
    try:
        # Read and validate file
        content = await file.read()
        if len(content) > 20 * 1024 * 1024:  # 20MB limit
            raise HTTPException(status_code=400, detail="File is over 20MB")

        # Convert to base64
        base64_data = base64.b64encode(content).decode("utf-8")
        
        # Create attachment without message_id
        file_type = file.content_type or mimetypes.guess_type(file.filename or "")[0]
        item = Attachment.to_dynamodb(base64_data, file_type)
        
        # Save to DynamoDB
        table = default_dynamodb_factory.get_table(ATTACHMENTS_TABLE)
        table.put_item(Item=item)
        
        # Return the attachment ID and file type
        return AttachmentUploadResponse(
            attachment_id=item["attachment_id"],
            file_type=item["file_type"],
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload file: {str(e)}",
        )
