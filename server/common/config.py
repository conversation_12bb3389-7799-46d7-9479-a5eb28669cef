import os
from typing import Dict

from dotenv import load_dotenv

load_dotenv()

# Service API keys configuration
SERVICE_API_KEYS: Dict[str, str] = {
    "gemini": os.getenv("GEMINI_API_KEY", ""),
    "openai": os.getenv("OPENAI_API_KEY", ""),
    "daily": os.getenv("DAILY_API_KEY", ""),
    "aws": os.getenv("AWS_ACCESS_KEY_ID", ""),  # For AWS services
}

# AWS configuration
AWS_CONFIG = {
    "region": os.getenv("AWS_REGION", "ap-northeast-1"),
    "access_key_id": os.getenv("AWS_ACCESS_KEY_ID", ""),
    "secret_access_key": os.getenv("AWS_SECRET_ACCESS_KEY", ""),
}

# Simple bot configuration (will be enhanced when pipecat is available)
DEFAULT_BOT_CONFIG = {
    "services": {
        "llm": "bedrock",  # Default to AWS Bedrock for HTTP pipeline
        "tts": "polly",
        "stt": "transcribe",
    },
    "config": [
        {
            "service": "llm",
            "options": [
                {"name": "model", "value": "apac.amazon.nova-lite-v1:0"},
                {"name": "initial_messages", "value": []},
                {"name": "run_on_config", "value": True},
            ],
        },
        {
            "service": "tts",
            "options": [
                {"name": "voice_id", "value": "Joanna"},
                {"name": "engine", "value": "neural"},
                {"name": "rate", "value": "1.0"},
            ],
        },
        {
            "service": "stt",
            "options": [
                {"name": "language_code", "value": "en-US"},
            ],
        },
    ],
}

# Language code mapping
LANGUAGE_CODES = {
    "en": "english",
    "es": "spanish",
    "fr": "french",
    "de": "german",
    "it": "italian",
    "pt": "portuguese",
    "ru": "russian",
    "ja": "japanese",
    "ko": "korean",
    "zh": "chinese",
}


def get_language_code(lang: str) -> str:
    """Get language code from language string"""
    return LANGUAGE_CODES.get(lang.lower(), "english")
