import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


# Pydantic Models for API
class ConversationModel(BaseModel):
    conversation_id: str
    title: Optional[str] = None
    archived: bool = False
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True


class ConversationCreateModel(BaseModel):
    title: Optional[str] = None

    class Config:
        from_attributes = True


class ConversationUpdateModel(BaseModel):
    title: Optional[str] = None
    archived: Optional[bool] = None

    class Config:
        from_attributes = True


class MessageCreateModel(BaseModel):
    content: Dict[str, Any]
    extra_metadata: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True
        extra = "allow"


class MessageModel(BaseModel):
    message_id: str
    conversation_id: str
    message_number: int
    content: Dict[str, Any]
    language_code: str = "english"
    created_at: datetime
    updated_at: datetime
    extra_metadata: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True


class AttachmentModel(BaseModel):
    attachment_id: str
    message_id: Optional[str] = None
    file_data: str
    file_type: str
    created_at: datetime

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True


class AttachmentUploadResponse(BaseModel):
    attachment_id: str
    file_type: str

    class Config:
        from_attributes = True


# DynamoDB Models (for conversion between DynamoDB and Pydantic)
class Conversation:
    @staticmethod
    def to_dynamodb(conversation: Union[ConversationCreateModel, ConversationUpdateModel], conversation_id: Optional[str] = None) -> Dict[str, Any]:
        """Convert a Pydantic model to a DynamoDB item"""
        now = datetime.utcnow().isoformat()
        item = {
            "conversation_id": conversation_id or str(uuid.uuid4()),
            "created_at": now,
            "updated_at": now,
        }
        
        if hasattr(conversation, "title") and conversation.title is not None:
            item["title"] = conversation.title
        else:
            item["title"] = "New conversation"
            
        if hasattr(conversation, "archived") and conversation.archived is not None:
            item["archived"] = conversation.archived
        else:
            item["archived"] = False
            
        return item
    
    @staticmethod
    def from_dynamodb(item: Dict[str, Any]) -> ConversationModel:
        """Convert a DynamoDB item to a Pydantic model"""
        return ConversationModel(
            conversation_id=item["conversation_id"],
            title=item.get("title", "New conversation"),
            archived=item.get("archived", False),
            created_at=datetime.fromisoformat(item["created_at"]),
            updated_at=datetime.fromisoformat(item["updated_at"]),
        )
    
    @staticmethod
    def update_dynamodb(item: Dict[str, Any], update_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a DynamoDB item with new data"""
        now = datetime.utcnow().isoformat()
        item.update(update_data)
        item["updated_at"] = now
        return item


class Message:
    @staticmethod
    def to_dynamodb(message: MessageCreateModel, conversation_id: str, message_number: int) -> Dict[str, Any]:
        """Convert a Pydantic model to a DynamoDB item"""
        now = datetime.utcnow().isoformat()
        item = {
            "message_id": str(uuid.uuid4()),
            "conversation_id": conversation_id,
            "message_number": message_number,
            "content": message.content,
            "language_code": "english",
            "created_at": now,
            "updated_at": now,
        }
        
        if message.extra_metadata:
            item["extra_metadata"] = message.extra_metadata
            
        return item
    
    @staticmethod
    def from_dynamodb(item: Dict[str, Any]) -> MessageModel:
        """Convert a DynamoDB item to a Pydantic model"""
        return MessageModel(
            message_id=item["message_id"],
            conversation_id=item["conversation_id"],
            message_number=int(item["message_number"]),
            content=item["content"],
            language_code=item.get("language_code", "english"),
            created_at=datetime.fromisoformat(item["created_at"]),
            updated_at=datetime.fromisoformat(item["updated_at"]),
            extra_metadata=item.get("extra_metadata"),
        )


class Attachment:
    @staticmethod
    def to_dynamodb(file_data: str, file_type: str, message_id: Optional[str] = None) -> Dict[str, Any]:
        """Convert attachment data to a DynamoDB item"""
        now = datetime.utcnow().isoformat()
        item = {
            "attachment_id": str(uuid.uuid4()),
            "file_data": file_data,
            "file_type": file_type,
            "created_at": now,
        }
        
        if message_id:
            item["message_id"] = message_id
            
        return item
    
    @staticmethod
    def from_dynamodb(item: Dict[str, Any]) -> AttachmentModel:
        """Convert a DynamoDB item to a Pydantic model"""
        return AttachmentModel(
            attachment_id=item["attachment_id"],
            message_id=item.get("message_id"),
            file_data=item["file_data"],
            file_type=item["file_type"],
            created_at=datetime.fromisoformat(item["created_at"]),
        )
