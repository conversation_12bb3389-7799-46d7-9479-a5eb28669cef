import os
import uuid
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import boto3
from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError
from dotenv import load_dotenv
from fastapi import HTTPException, status
from loguru import logger
from pydantic import BaseModel

load_dotenv()

# DynamoDB table names
CONVERSATIONS_TABLE = "Conversations"
MESSAGES_TABLE = "Messages"
ATTACHMENTS_TABLE = "Attachments"


class DynamoDBSessionFactory:
    _instance: "DynamoDBSessionFactory | None" = None
    _initialized: bool = False
    _client = None
    _resource = None

    def __new__(cls) -> "DynamoDBSessionFactory":
        if cls._instance is None:
            cls._instance = super(DynamoDBSessionFactory, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        # Get AWS credentials from environment variables
        aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
        aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        aws_region = os.getenv("AWS_REGION", "us-east-1")

        # Initialize DynamoDB client and resource
        kwargs = {
            "region_name": aws_region,
        }

        if aws_access_key_id and aws_secret_access_key:
            kwargs["aws_access_key_id"] = aws_access_key_id
            kwargs["aws_secret_access_key"] = aws_secret_access_key

        self._client = boto3.client("dynamodb", **kwargs)
        self._resource = boto3.resource("dynamodb", **kwargs)
        self._initialized = True

    @property
    def client(self):
        if self._client is None:
            raise RuntimeError("DynamoDB client not initialized")
        return self._client

    @property
    def resource(self):
        if self._resource is None:
            raise RuntimeError("DynamoDB resource not initialized")
        return self._resource

    def get_table(self, table_name: str):
        return self._resource.Table(table_name)

    async def initialize_tables(self):
        """Check if tables exist, create them if they don't"""
        try:
            existing_tables = self._client.list_tables()["TableNames"]

            # Create Conversations table if it doesn't exist
            if CONVERSATIONS_TABLE not in existing_tables:
                logger.info(f"Creating {CONVERSATIONS_TABLE} table")
                self._client.create_table(
                    TableName=CONVERSATIONS_TABLE,
                    KeySchema=[
                        {
                            "AttributeName": "conversation_id",
                            "KeyType": "HASH",
                        },  # Partition key
                    ],
                    AttributeDefinitions=[
                        {"AttributeName": "conversation_id", "AttributeType": "S"},
                    ],
                    ProvisionedThroughput={
                        "ReadCapacityUnits": 5,
                        "WriteCapacityUnits": 5,
                    },
                )
                logger.info(f"{CONVERSATIONS_TABLE} table created")

            # Create Messages table if it doesn't exist
            if MESSAGES_TABLE not in existing_tables:
                logger.info(f"Creating {MESSAGES_TABLE} table")
                self._client.create_table(
                    TableName=MESSAGES_TABLE,
                    KeySchema=[
                        {
                            "AttributeName": "message_id",
                            "KeyType": "HASH",
                        },  # Partition key
                    ],
                    AttributeDefinitions=[
                        {"AttributeName": "message_id", "AttributeType": "S"},
                        {"AttributeName": "conversation_id", "AttributeType": "S"},
                    ],
                    GlobalSecondaryIndexes=[
                        {
                            "IndexName": "ConversationIdIndex",
                            "KeySchema": [
                                {"AttributeName": "conversation_id", "KeyType": "HASH"},
                            ],
                            "Projection": {"ProjectionType": "ALL"},
                            "ProvisionedThroughput": {
                                "ReadCapacityUnits": 5,
                                "WriteCapacityUnits": 5,
                            },
                        }
                    ],
                    ProvisionedThroughput={
                        "ReadCapacityUnits": 5,
                        "WriteCapacityUnits": 5,
                    },
                )
                logger.info(f"{MESSAGES_TABLE} table created")

            # Create Attachments table if it doesn't exist
            if ATTACHMENTS_TABLE not in existing_tables:
                logger.info(f"Creating {ATTACHMENTS_TABLE} table")
                self._client.create_table(
                    TableName=ATTACHMENTS_TABLE,
                    KeySchema=[
                        {
                            "AttributeName": "attachment_id",
                            "KeyType": "HASH",
                        },  # Partition key
                    ],
                    AttributeDefinitions=[
                        {"AttributeName": "attachment_id", "AttributeType": "S"},
                        {"AttributeName": "message_id", "AttributeType": "S"},
                    ],
                    GlobalSecondaryIndexes=[
                        {
                            "IndexName": "MessageIdIndex",
                            "KeySchema": [
                                {"AttributeName": "message_id", "KeyType": "HASH"},
                            ],
                            "Projection": {"ProjectionType": "ALL"},
                            "ProvisionedThroughput": {
                                "ReadCapacityUnits": 5,
                                "WriteCapacityUnits": 5,
                            },
                        }
                    ],
                    ProvisionedThroughput={
                        "ReadCapacityUnits": 5,
                        "WriteCapacityUnits": 5,
                    },
                )
                logger.info(f"{ATTACHMENTS_TABLE} table created")

            # Wait for tables to be created
            waiter = self._client.get_waiter("table_exists")
            for table_name in [CONVERSATIONS_TABLE, MESSAGES_TABLE, ATTACHMENTS_TABLE]:
                if table_name not in existing_tables:
                    waiter.wait(TableName=table_name)
                    logger.info(f"Table {table_name} is now available")

            logger.info("DynamoDB tables initialized")
        except Exception as e:
            logger.error(f"Error initializing DynamoDB tables: {str(e)}")
            raise e


# Create a default session factory for convenience
default_dynamodb_factory = DynamoDBSessionFactory()


# Helper functions for DynamoDB operations
async def get_conversation_by_id(conversation_id: str) -> Optional[Dict[str, Any]]:
    """Get a conversation by ID"""
    try:
        table = default_dynamodb_factory.get_table(CONVERSATIONS_TABLE)
        response = table.get_item(Key={"conversation_id": conversation_id})
        return response.get("Item")
    except ClientError as e:
        logger.error(f"Error getting conversation {conversation_id}: {str(e)}")
        return None


async def get_messages_by_conversation_id(conversation_id: str) -> List[Dict[str, Any]]:
    """Get all messages for a conversation"""
    try:
        table = default_dynamodb_factory.get_table(MESSAGES_TABLE)
        response = table.query(
            IndexName="ConversationIdIndex",
            KeyConditionExpression=Key("conversation_id").eq(conversation_id),
        )
        # Sort messages by message_number
        messages = sorted(
            response.get("Items", []), key=lambda x: x.get("message_number", 0)
        )
        return messages
    except ClientError as e:
        logger.error(
            f"Error getting messages for conversation {conversation_id}: {str(e)}"
        )
        return []


async def get_max_message_number(conversation_id: str) -> int:
    """Get the maximum message number for a conversation"""
    messages = await get_messages_by_conversation_id(conversation_id)
    if not messages:
        return 0
    return max(int(msg.get("message_number", 0)) for msg in messages)
