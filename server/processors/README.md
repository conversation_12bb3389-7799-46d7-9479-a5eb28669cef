# DynamoDB Conversation Storage

This directory contains a custom storage module for storing conversation data in AWS DynamoDB.

## Overview

The `DynamoDBConversationStorage` class provides methods to store conversation data in DynamoDB using the existing models and database functions. It can be used to capture both user and assistant messages from the Pipecat pipeline.

## Features

- Creates a new conversation when a session starts
- Adds messages to the conversation as they occur
- Updates conversation metadata as needed
- Supports storing system messages (optional)
- <PERSON>les errors gracefully without failing the pipeline

## Usage

To use the storage in a bot pipeline:

```python
from processors.dynamodb_conversation_storage import get_storage

# Create DynamoDB conversation storage
db_storage = get_storage(
    conversation_id="your-conversation-id",  # Optional, will generate UUID if not provided
    conversation_title="Your Conversation Title",  # Optional
    store_system_messages=True,  # Optional, defaults to True
)

# Store a message
await db_storage.store_message("user", "Hello, how are you?")
await db_storage.store_message("assistant", "I'm doing well, thank you for asking!")

# With context aggregator
context_aggregator.user().add_callback(async def store_user_message(frame):
    if frame.data and isinstance(frame.data, dict):
        if "role" in frame.data and frame.data.get("role") == "user":
            await db_storage.store_message(frame.data["role"], frame.data["content"])
    return [frame]
)
```

## DynamoDB Schema

The processor uses the existing DynamoDB tables:

1. **Conversations** - Stores conversation metadata
   - Primary Key: `conversation_id`
   - Attributes: `title`, `archived`, `created_at`, `updated_at`

2. **Messages** - Stores messages
   - Primary Key: `message_id`
   - Global Secondary Index: `ConversationIdIndex` on `conversation_id`
   - Attributes: `conversation_id`, `message_number`, `content`, `language_code`, `created_at`, `updated_at`, `extra_metadata`

## Implementation Details

The processor:

1. Initializes by creating or retrieving a conversation
2. Intercepts message frames from the pipeline
3. Stores messages in DynamoDB with appropriate metadata
4. Updates conversation timestamps
5. Handles errors gracefully to prevent pipeline failures

## Requirements

- AWS credentials configured in environment variables
- DynamoDB tables created (handled by server initialization)
