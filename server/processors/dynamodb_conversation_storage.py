"""
DynamoDB Conversation Storage for storing conversation data in AWS DynamoDB.

This module provides functions to store conversation data in DynamoDB
using the existing models and database functions.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, Optional

import logging

from common.dynamodb import (
    CONVERSATIONS_TABLE,
    MESSAGES_TABLE,
    default_dynamodb_factory,
    get_conversation_by_id,
    get_max_message_number,
)
from common.models import (
    Conversation,
    ConversationCreateModel,
    Message,
    MessageCreateModel,
)

# Set up logging
logger = logging.getLogger(__name__)


class DynamoDBConversationStorage:
    """
    Storage class for conversation data in AWS DynamoDB.
    
    This class provides methods to store conversation data in DynamoDB
    using the existing models and database functions.
    """
    
    def __init__(
        self,
        conversation_id: Optional[str] = None,
        conversation_title: Optional[str] = None,
        store_system_messages: bool = True,
    ):
        """
        Initialize the DynamoDB Conversation Storage.
        
        Args:
            conversation_id: Optional ID for the conversation. If not provided, a new ID will be generated.
            conversation_title: Optional title for the conversation.
            store_system_messages: Whether to store system messages.
        """
        self.conversation_id = conversation_id or str(uuid.uuid4())
        self.conversation_title = conversation_title or "Voice Conversation"
        self.store_system_messages = store_system_messages
        self.conversation = None
        self.initialized = False
        
    async def initialize(self):
        """Initialize by creating or retrieving the conversation."""
        if self.initialized:
            return
        
        try:
            # Check if conversation exists
            self.conversation = await get_conversation_by_id(self.conversation_id)
            
            if not self.conversation:
                # Create a new conversation
                conversation_create = ConversationCreateModel(
                    title=self.conversation_title
                )
                conversation_item = Conversation.to_dynamodb(
                    conversation_create, self.conversation_id
                )
                
                # Save to DynamoDB
                table = default_dynamodb_factory.get_table(CONVERSATIONS_TABLE)
                table.put_item(Item=conversation_item)
                
                # Retrieve the created conversation
                self.conversation = await get_conversation_by_id(self.conversation_id)
                logger.info(f"Created new conversation: {self.conversation_id}")
            else:
                logger.info(f"Using existing conversation: {self.conversation_id}")
            
            self.initialized = True
        except Exception as e:
            logger.error(f"Error initializing DynamoDB Conversation Storage: {str(e)}")
            # Continue without failing
            self.initialized = True
    
    async def store_message(self, role: str, content: Any):
        """
        Store a message in DynamoDB.
        
        Args:
            role: The role of the message sender (user, assistant, system).
            content: The content of the message.
        """
        # Skip system messages if configured to do so
        if role == "system" and not self.store_system_messages:
            return
            
        await self.initialize()
        
        if not self.conversation:
            logger.warning("Cannot store message: conversation not initialized")
            return
        
        try:
            # Create message model
            message_create = MessageCreateModel(
                content={
                    "role": role,
                    "content": content,
                }
            )
            
            # Get the next message number
            message_number = await get_max_message_number(self.conversation_id) + 1
            
            # Convert to DynamoDB item
            item = Message.to_dynamodb(message_create, self.conversation_id, message_number)
            
            # Save to DynamoDB
            messages_table = default_dynamodb_factory.get_table(MESSAGES_TABLE)
            messages_table.put_item(Item=item)
            
            # Update the conversation's updated_at timestamp
            self.conversation["updated_at"] = datetime.now().isoformat()
            conversations_table = default_dynamodb_factory.get_table(
                CONVERSATIONS_TABLE
            )
            conversations_table.put_item(Item=self.conversation)
            
            logger.info(f"Stored message in conversation {self.conversation_id}: {role}")
        except Exception as e:
            logger.error(f"Error storing message: {str(e)}")


# Create a singleton instance for convenience
conversation_storage = None


def get_storage(
    conversation_id: Optional[str] = None,
    conversation_title: Optional[str] = None,
    store_system_messages: bool = True,
) -> DynamoDBConversationStorage:
    """
    Get a DynamoDB Conversation Storage instance.
    
    Args:
        conversation_id: Optional ID for the conversation. If not provided, a new ID will be generated.
        conversation_title: Optional title for the conversation.
        store_system_messages: Whether to store system messages.
        
    Returns:
        A DynamoDB Conversation Storage instance.
    """
    global conversation_storage
    
    if conversation_storage is None or (conversation_id and conversation_storage.conversation_id != conversation_id):
        conversation_storage = DynamoDBConversationStorage(
            conversation_id=conversation_id,
            conversation_title=conversation_title,
            store_system_messages=store_system_messages,
        )
    
    return conversation_storage
