"""
DynamoDB Conversation Processor for storing conversation data in AWS DynamoDB.

This processor intercepts messages from the pipeline and stores them in DynamoDB
using the existing models and database functions.
"""

import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger
from pydantic import BaseModel

from common.dynamodb import (
    CONVERSATIONS_TABLE,
    MESSAGES_TABLE,
    default_dynamodb_factory,
    get_conversation_by_id,
    get_max_message_number,
)
from common.models import (
    Conversation,
    ConversationCreateModel,
    Message,
    MessageCreateModel,
)
from pipecat.frames.frames import Frame
from pipecat.processors.frame_processor import Processor


class DynamoDBConversationProcessorParams(BaseModel):
    """Parameters for the DynamoDB Conversation Processor."""

    conversation_id: Optional[str] = None
    conversation_title: Optional[str] = None
    store_system_messages: bool = True


class DynamoDBConversationProcessor(Processor):
    """
    Processor that stores conversation data in AWS DynamoDB.

    This processor intercepts messages from the pipeline and stores them in DynamoDB
    using the existing models and database functions.
    """

    def __init__(self, params: Optional[DynamoDBConversationProcessorParams] = None):
        """
        Initialize the DynamoDB Conversation Processor.

        Args:
            params: Optional parameters for the processor.
        """
        super().__init__()
        self.params = params or DynamoDBConversationProcessorParams()
        self.conversation_id = self.params.conversation_id or str(uuid.uuid4())
        self.conversation = None
        self.initialized = False

    async def initialize(self):
        """Initialize the processor by creating or retrieving the conversation."""
        if self.initialized:
            return

        try:
            # Check if conversation exists
            self.conversation = await get_conversation_by_id(self.conversation_id)

            if not self.conversation:
                # Create a new conversation
                conversation_create = ConversationCreateModel(
                    title=self.params.conversation_title or "Voice Conversation"
                )
                conversation_item = Conversation.to_dynamodb(
                    conversation_create, self.conversation_id
                )

                # Save to DynamoDB
                table = default_dynamodb_factory.get_table(CONVERSATIONS_TABLE)
                table.put_item(Item=conversation_item)

                # Retrieve the created conversation
                self.conversation = await get_conversation_by_id(self.conversation_id)
                logger.info(f"Created new conversation: {self.conversation_id}")
            else:
                logger.info(f"Using existing conversation: {self.conversation_id}")

            self.initialized = True
        except Exception as e:
            logger.error(
                f"Error initializing DynamoDB Conversation Processor: {str(e)}"
            )
            # Continue without failing the pipeline
            self.initialized = True

    async def process_frame(self, frame: Frame) -> List[Frame]:
        """
        Process a frame from the pipeline.

        Args:
            frame: The frame to process.

        Returns:
            The processed frame.
        """
        await self.initialize()

        try:
            # Check if this is a message frame
            if frame.data and isinstance(frame.data, dict):
                # Check if this is a message from the context aggregator
                if "role" in frame.data and "content" in frame.data:
                    role = frame.data.get("role")
                    content = frame.data.get("content")

                    # Skip system messages if configured to do so
                    if role == "system" and not self.params.store_system_messages:
                        return [frame]

                    # Store the message
                    await self.store_message(role, content)

            # Always pass the frame through
            return [frame]
        except Exception as e:
            logger.error(f"Error in DynamoDB Conversation Processor: {str(e)}")
            # Continue without failing the pipeline
            return [frame]

    async def store_message(self, role: str, content: Any):
        """
        Store a message in DynamoDB.

        Args:
            role: The role of the message sender (user, assistant, system).
            content: The content of the message.
        """
        if not self.conversation:
            logger.warning("Cannot store message: conversation not initialized")
            return

        try:
            # Create message model
            message_create = MessageCreateModel(
                content={
                    "role": role,
                    "content": content,
                }
            )

            # Get the next message number
            message_number = await get_max_message_number(self.conversation_id) + 1

            # Convert to DynamoDB item
            item = Message.to_dynamodb(
                message_create, self.conversation_id, message_number
            )

            # Save to DynamoDB
            messages_table = default_dynamodb_factory.get_table(MESSAGES_TABLE)
            messages_table.put_item(Item=item)

            # Update the conversation's updated_at timestamp
            self.conversation["updated_at"] = datetime.utcnow().isoformat()
            conversations_table = default_dynamodb_factory.get_table(
                CONVERSATIONS_TABLE
            )
            conversations_table.put_item(Item=self.conversation)

            logger.info(
                f"Stored message in conversation {self.conversation_id}: {role}"
            )
        except Exception as e:
            logger.error(f"Error storing message: {str(e)}")


def create_processor(
    conversation_id: Optional[str] = None,
    conversation_title: Optional[str] = None,
    store_system_messages: bool = True,
) -> DynamoDBConversationProcessor:
    """
    Create a DynamoDB Conversation Processor.

    Args:
        conversation_id: Optional ID for the conversation. If not provided, a new ID will be generated.
        conversation_title: Optional title for the conversation.
        store_system_messages: Whether to store system messages.

    Returns:
        A DynamoDB Conversation Processor.
    """
    params = DynamoDBConversationProcessorParams(
        conversation_id=conversation_id,
        conversation_title=conversation_title,
        store_system_messages=store_system_messages,
    )
    return DynamoDBConversationProcessor(params)
